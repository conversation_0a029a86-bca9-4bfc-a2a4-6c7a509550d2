
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AgentSalud - Management APIs Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; }
        .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🏥 AgentSalud MVP - Management APIs Test</h1>
    <p><strong>Organization:</strong> VisualCare (927cecbe-d9e5-43a4-b9d0-25f942ededc4)</p>
    
    <div id="results"></div>
    
    <button onclick="testAllAPIs()">🧪 Test All APIs</button>
    <button onclick="testUsersAPI()">👥 Test Users API</button>
    <button onclick="testPatientsAPI()">🏥 Test Patients API</button>
    <button onclick="clearResults()">🗑️ Clear Results</button>

    <script>
        const ORGANIZATION_ID = '927cecbe-d9e5-43a4-b9d0-25f942ededc4';
        const resultsDiv = document.getElementById('results');

        function addResult(title, content, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = `<h3>${title}</h3><div>${content}</div>`;
            resultsDiv.appendChild(div);
        }

        function clearResults() {
            resultsDiv.innerHTML = '';
        }

        async function testAPI(name, url, expectedCount) {
            try {
                addResult(`Testing ${name}`, `Calling: ${url}`, 'warning');
                
                const response = await fetch(url);
                const result = await response.json();
                
                if (!response.ok) {
                    addResult(`❌ ${name} Failed`, `Status: ${response.status}<br>Error: ${result.error || 'Unknown error'}`, 'error');
                    return;
                }
                
                const actualCount = result.data ? result.data.length : 0;
                const match = actualCount === expectedCount;
                
                addResult(
                    `${match ? '✅' : '❌'} ${name} Result`,
                    `Expected: ${expectedCount}<br>Actual: ${actualCount}<br>Status: ${match ? 'PASSED' : 'FAILED'}`,
                    match ? 'success' : 'error'
                );
                
                if (result.data && result.data.length > 0) {
                    addResult(`📋 ${name} Sample Data`, `<pre>${JSON.stringify(result.data[0], null, 2)}</pre>`);
                }
                
            } catch (error) {
                addResult(`❌ ${name} Error`, `${error.message}`, 'error');
            }
        }

        async function testUsersAPI() {
            await testAPI('Users API', `/api/users?organizationId=${ORGANIZATION_ID}`, 13);
        }

        async function testPatientsAPI() {
            await testAPI('Patients API', `/api/patients?organizationId=${ORGANIZATION_ID}`, 3);
        }

        async function testAllAPIs() {
            clearResults();
            addResult('🚀 Starting API Tests', 'Testing all management APIs...', 'warning');
            
            await testUsersAPI();
            await testPatientsAPI();
            
            // Test other APIs if they exist
            await testAPI('Doctors API', `/api/doctors?organizationId=${ORGANIZATION_ID}`, 5);
            await testAPI('Appointments API', `/api/appointments?organizationId=${ORGANIZATION_ID}`, 10);
            
            addResult('✅ Tests Complete', 'All API tests have been executed.', 'success');
        }

        // Auto-run tests on page load
        window.addEventListener('load', () => {
            addResult('🏥 AgentSalud Management APIs Tester', 'Ready to test APIs. Click "Test All APIs" to begin.', 'warning');
        });
    </script>
</body>
</html>
  