/**
 * Tests for AIEnhancedRescheduleModal Component
 * 
 * Pruebas completas para el modal de reagendamiento con IA que integra
 * todas las mejoras de las Fases 1-3: WeeklyAvailabilitySelector,
 * SmartSuggestionsEngine y AIContextProcessor
 * 
 * <AUTHOR> MVP Team - AI Enhancement Tests
 * @version 3.0.0
 */

import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import '@testing-library/jest-dom';
import AIEnhancedRescheduleModal from '@/components/appointments/AIEnhancedRescheduleModal';
import { AppointmentData } from '@/components/appointments/AppointmentCard';

// Mock de WeeklyAvailabilitySelector
jest.mock('@/components/appointments/WeeklyAvailabilitySelector', () => {
  return function MockWeeklyAvailabilitySelector({ 
    title, 
    subtitle, 
    selectedDate, 
    onDateSelect, 
    enableSmartSuggestions,
    aiContext 
  }: any) {
    return (
      <div data-testid="weekly-availability-selector">
        <h3>{title}</h3>
        <p>{subtitle}</p>
        <div data-testid="ai-context-present">{aiContext ? 'AI Context Available' : 'No AI Context'}</div>
        <div data-testid="smart-suggestions">{enableSmartSuggestions ? 'Smart Suggestions Enabled' : 'Smart Suggestions Disabled'}</div>
        <button 
          onClick={() => onDateSelect('2025-06-01', '10:00')}
          data-testid="mock-date-select"
        >
          Select Date
        </button>
        <div data-testid="selected-date">{selectedDate}</div>
      </div>
    );
  };
});

// Mock de AIContextProcessor
jest.mock('@/lib/ai/AIContextProcessor', () => ({
  AIContextProcessor: jest.fn(),
}));

// Mock de SmartSuggestionsEngine
jest.mock('@/lib/ai/SmartSuggestionsEngine', () => ({
  SmartSuggestionsEngine: jest.fn(),
}));

// Mock fetch global
global.fetch = jest.fn();

describe('AIEnhancedRescheduleModal', () => {
  const mockAppointment: AppointmentData = {
    id: 'test-appointment-1',
    appointment_date: '2025-05-30',
    start_time: '10:00:00',
    end_time: '11:00:00',
    status: 'confirmed',
    notes: 'Test appointment',
    created_at: '2025-05-29T10:00:00Z',
    doctor: [{
      id: 'doctor-1',
      specialization: 'Cardiología',
      profiles: [{
        first_name: 'Juan',
        last_name: 'Pérez'
      }]
    }],
    service: [{
      id: 'service-1',
      name: 'Consulta Cardiológica',
      description: 'Consulta especializada'
    }],
    location: [{
      id: 'location-1',
      name: 'Sede Principal',
      address: 'Calle 123 #45-67'
    }]
  };

  const defaultProps = {
    isOpen: true,
    appointment: mockAppointment,
    organizationId: 'org-123',
    onConfirm: jest.fn(),
    onCancel: jest.fn(),
    loading: false,
    error: null
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Renderizado básico', () => {
    it('debe renderizar el modal cuando está abierto', () => {
      render(<AIEnhancedRescheduleModal {...defaultProps} />);
      
      expect(screen.getByText('Reagendar Cita con IA')).toBeInTheDocument();
      expect(screen.getByText('Potenciado por IA')).toBeInTheDocument();
      expect(screen.getByText('Selecciona una nueva fecha con sugerencias inteligentes')).toBeInTheDocument();
    });

    it('no debe renderizar cuando isOpen es false', () => {
      render(<AIEnhancedRescheduleModal {...defaultProps} isOpen={false} />);
      
      expect(screen.queryByText('Reagendar Cita con IA')).not.toBeInTheDocument();
    });

    it('no debe renderizar cuando appointment es null', () => {
      render(<AIEnhancedRescheduleModal {...defaultProps} appointment={null} />);
      
      expect(screen.queryByText('Reagendar Cita con IA')).not.toBeInTheDocument();
    });
  });

  describe('Información de la cita actual', () => {
    it('debe mostrar la información de la cita actual', () => {
      render(<AIEnhancedRescheduleModal {...defaultProps} />);

      expect(screen.getByText('Consulta Cardiológica')).toBeInTheDocument();
      expect(screen.getByText('Dr. Juan Pérez')).toBeInTheDocument();

      // Use more specific selectors to avoid conflicts
      const currentDateElements = screen.getAllByText('2025-05-30');
      expect(currentDateElements.length).toBeGreaterThan(0);

      expect(screen.getByText('10:00')).toBeInTheDocument();
      expect(screen.getByText('Sede Principal')).toBeInTheDocument();
    });

    it('debe manejar datos faltantes graciosamente', () => {
      const incompleteAppointment = {
        ...mockAppointment,
        doctor: [],
        service: [],
        location: []
      };

      render(<AIEnhancedRescheduleModal {...defaultProps} appointment={incompleteAppointment} />);
      
      expect(screen.getByText('Consulta General')).toBeInTheDocument();
      expect(screen.getByText('Doctor no especificado')).toBeInTheDocument();
    });
  });

  describe('Modo IA vs Modo Manual', () => {
    it('debe iniciar en modo IA por defecto', () => {
      render(<AIEnhancedRescheduleModal {...defaultProps} />);
      
      expect(screen.getByText('Modo IA')).toBeInTheDocument();
      expect(screen.getByTestId('weekly-availability-selector')).toBeInTheDocument();
    });

    it('debe alternar entre modo IA y manual', async () => {
      render(<AIEnhancedRescheduleModal {...defaultProps} />);
      
      // Inicialmente en modo IA
      expect(screen.getByText('Modo IA')).toBeInTheDocument();
      expect(screen.getByTestId('weekly-availability-selector')).toBeInTheDocument();
      
      // Cambiar a modo manual
      fireEvent.click(screen.getByText('Modo IA'));
      
      await waitFor(() => {
        expect(screen.getByText('Modo Manual')).toBeInTheDocument();
        expect(screen.getByText('Selección Manual')).toBeInTheDocument();
        expect(screen.queryByTestId('weekly-availability-selector')).not.toBeInTheDocument();
      });
    });

    it('debe mostrar campos manuales en modo manual', async () => {
      render(<AIEnhancedRescheduleModal {...defaultProps} />);
      
      // Cambiar a modo manual
      fireEvent.click(screen.getByText('Modo IA'));
      
      await waitFor(() => {
        expect(screen.getByLabelText('Nueva Fecha')).toBeInTheDocument();
        expect(screen.getByLabelText('Nueva Hora')).toBeInTheDocument();
      });
    });
  });

  describe('Integración con WeeklyAvailabilitySelector', () => {
    it('debe pasar las props correctas a WeeklyAvailabilitySelector', () => {
      render(<AIEnhancedRescheduleModal {...defaultProps} />);
      
      const selector = screen.getByTestId('weekly-availability-selector');
      expect(selector).toBeInTheDocument();
      
      expect(screen.getByText('¿Cuándo te gustaría reagendar?')).toBeInTheDocument();
      expect(screen.getByText('Sugerencias inteligentes basadas en tu cita original')).toBeInTheDocument();
      expect(screen.getByTestId('ai-context-present')).toHaveTextContent('AI Context Available');
      expect(screen.getByTestId('smart-suggestions')).toHaveTextContent('Smart Suggestions Enabled');
    });

    it('debe manejar la selección de fecha desde WeeklyAvailabilitySelector', async () => {
      render(<AIEnhancedRescheduleModal {...defaultProps} />);
      
      const selectButton = screen.getByTestId('mock-date-select');
      fireEvent.click(selectButton);
      
      await waitFor(() => {
        expect(screen.getByTestId('selected-date')).toHaveTextContent('2025-06-01');
      });
    });
  });

  describe('Contexto de IA', () => {
    it('debe generar contexto de IA basado en la cita original', () => {
      render(<AIEnhancedRescheduleModal {...defaultProps} />);
      
      expect(screen.getByText('Análisis Inteligente:')).toBeInTheDocument();
      expect(screen.getByText(/Manteniendo tu preferencia de horario/)).toBeInTheDocument();
      expect(screen.getByText(/Basado en tu cita original del/)).toBeInTheDocument();
      expect(screen.getByText(/Mostrando opciones similares/)).toBeInTheDocument();
    });

    it('debe determinar correctamente el rango de tiempo preferido', () => {
      // Cita en la mañana
      const morningAppointment = {
        ...mockAppointment,
        start_time: '09:00:00'
      };

      render(<AIEnhancedRescheduleModal {...defaultProps} appointment={morningAppointment} />);
      expect(screen.getByText(/horario matutino/)).toBeInTheDocument();
    });

    it('debe manejar citas en la tarde', () => {
      const afternoonAppointment = {
        ...mockAppointment,
        start_time: '15:00:00'
      };

      render(<AIEnhancedRescheduleModal {...defaultProps} appointment={afternoonAppointment} />);
      expect(screen.getByText(/horario vespertino/)).toBeInTheDocument();
    });

    it('debe excluir la fecha actual de las sugerencias', () => {
      // Mock para controlar la fecha actual
      const mockDate = new Date('2025-05-30T10:00:00Z');
      const originalDate = global.Date;
      global.Date = jest.fn(() => mockDate) as any;
      global.Date.now = originalDate.now;
      global.Date.parse = originalDate.parse;
      global.Date.UTC = originalDate.UTC;

      render(<AIEnhancedRescheduleModal {...defaultProps} />);

      // Verificar que el contexto de IA se genera correctamente
      expect(screen.getByText(/basado en tu cita original/i)).toBeInTheDocument();

      // Las sugerencias no deben incluir la fecha actual
      // Esto se valida indirectamente a través del comportamiento del WeeklyAvailabilitySelector
      expect(screen.getAllByText(/sugerencias inteligentes/i)).toHaveLength(2);

      // Restore original Date
      global.Date = originalDate;
    });

    it('debe generar fechas futuras en formato correcto', () => {
      // Mock para controlar la fecha actual
      const mockDate = new Date('2025-05-30T15:30:00Z');
      const originalDate = global.Date;
      global.Date = jest.fn(() => mockDate) as any;
      global.Date.now = originalDate.now;
      global.Date.parse = originalDate.parse;
      global.Date.UTC = originalDate.UTC;

      render(<AIEnhancedRescheduleModal {...defaultProps} />);

      // Verificar que el contexto de IA se genera correctamente
      expect(screen.getByText(/basado en tu cita original/i)).toBeInTheDocument();
      expect(screen.getByText(/manteniendo tu preferencia/i)).toBeInTheDocument();

      // Restore original Date
      global.Date = originalDate;
    });
  });

  describe('Validación y envío del formulario', () => {
    it('debe deshabilitar el botón de envío cuando faltan datos', () => {
      render(<AIEnhancedRescheduleModal {...defaultProps} />);

      // The button should be disabled when no date/time is selected
      const submitButton = screen.getByRole('button', { name: /confirmar reagendado/i });
      expect(submitButton).toBeDisabled();
    });

    it('debe habilitar el botón cuando hay fecha y hora', async () => {
      render(<AIEnhancedRescheduleModal {...defaultProps} />);
      
      // Seleccionar fecha usando el mock
      fireEvent.click(screen.getByTestId('mock-date-select'));
      
      await waitFor(() => {
        const submitButton = screen.getByText('Confirmar Reagendado');
        expect(submitButton).not.toBeDisabled();
      });
    });

    it('debe llamar onConfirm con los datos correctos', async () => {
      const mockOnConfirm = jest.fn().mockResolvedValue(undefined);
      
      render(<AIEnhancedRescheduleModal {...defaultProps} onConfirm={mockOnConfirm} />);
      
      // Seleccionar fecha
      fireEvent.click(screen.getByTestId('mock-date-select'));
      
      await waitFor(() => {
        const submitButton = screen.getByText('Confirmar Reagendado');
        expect(submitButton).not.toBeDisabled();
      });
      
      // Enviar formulario
      fireEvent.click(screen.getByText('Confirmar Reagendado'));
      
      await waitFor(() => {
        expect(mockOnConfirm).toHaveBeenCalledWith('test-appointment-1', '2025-06-01', '10:00');
      });
    });
  });

  describe('Estados de carga y error', () => {
    it('debe mostrar estado de carga', () => {
      render(<AIEnhancedRescheduleModal {...defaultProps} loading={true} />);

      // When loading, the button should show "Reagendando..." and be disabled
      const submitButton = screen.getByRole('button', { name: /reagendando/i });
      expect(submitButton).toBeDisabled();
    });

    it('debe mostrar mensaje de error', () => {
      const errorMessage = 'Error al reagendar la cita';
      render(<AIEnhancedRescheduleModal {...defaultProps} error={errorMessage} />);
      
      expect(screen.getByText(errorMessage)).toBeInTheDocument();
    });

    it('debe mostrar estado de envío', async () => {
      const mockOnConfirm = jest.fn().mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)));
      
      render(<AIEnhancedRescheduleModal {...defaultProps} onConfirm={mockOnConfirm} />);
      
      // Seleccionar fecha
      fireEvent.click(screen.getByTestId('mock-date-select'));
      
      await waitFor(() => {
        const submitButton = screen.getByText('Confirmar Reagendado');
        expect(submitButton).not.toBeDisabled();
      });
      
      // Enviar formulario
      fireEvent.click(screen.getByText('Confirmar Reagendado'));
      
      await waitFor(() => {
        expect(screen.getByText('Reagendando...')).toBeInTheDocument();
      });
    });
  });

  describe('Interacciones del modal', () => {
    it('debe llamar onCancel cuando se hace clic en cancelar', () => {
      const mockOnCancel = jest.fn();
      render(<AIEnhancedRescheduleModal {...defaultProps} onCancel={mockOnCancel} />);
      
      fireEvent.click(screen.getByText('Cancelar'));
      expect(mockOnCancel).toHaveBeenCalled();
    });

    it('debe llamar onCancel cuando se hace clic en la X', () => {
      const mockOnCancel = jest.fn();
      render(<AIEnhancedRescheduleModal {...defaultProps} onCancel={mockOnCancel} />);
      
      fireEvent.click(screen.getByLabelText('Cerrar modal'));
      expect(mockOnCancel).toHaveBeenCalled();
    });

    it('debe llamar onCancel cuando se hace clic en el backdrop', () => {
      const mockOnCancel = jest.fn();
      render(<AIEnhancedRescheduleModal {...defaultProps} onCancel={mockOnCancel} />);
      
      // Hacer clic en el backdrop (primer div con clase fixed)
      const backdrop = document.querySelector('.fixed.inset-0.bg-black');
      if (backdrop) {
        fireEvent.click(backdrop);
        expect(mockOnCancel).toHaveBeenCalled();
      }
    });
  });

  describe('Accesibilidad', () => {
    it('debe tener etiquetas aria apropiadas', () => {
      render(<AIEnhancedRescheduleModal {...defaultProps} />);
      
      expect(screen.getByLabelText('Cerrar modal')).toBeInTheDocument();
    });

    it('debe manejar navegación por teclado', () => {
      render(<AIEnhancedRescheduleModal {...defaultProps} />);
      
      const closeButton = screen.getByLabelText('Cerrar modal');
      expect(closeButton).toBeInTheDocument();
      
      // Verificar que el botón puede recibir foco
      closeButton.focus();
      expect(document.activeElement).toBe(closeButton);
    });
  });

  describe('Integración multi-tenant', () => {
    it('debe pasar organizationId a WeeklyAvailabilitySelector', () => {
      render(<AIEnhancedRescheduleModal {...defaultProps} organizationId="test-org-123" />);

      // El organizationId se pasa internamente al WeeklyAvailabilitySelector
      // Esto se verifica indirectamente a través de la presencia del componente
      expect(screen.getByTestId('weekly-availability-selector')).toBeInTheDocument();
    });
  });

  describe('Deduplicación de Time Slots', () => {
    beforeEach(() => {
      // Mock fetch para simular respuesta con time slots duplicados
      global.fetch = jest.fn().mockResolvedValue({
        ok: true,
        json: async () => ({
          success: true,
          data: [
            { start_time: '09:00', doctor_id: 'doc1', available: true },
            { start_time: '09:00', doctor_id: 'doc1', available: true }, // Duplicado
            { start_time: '10:00', doctor_id: 'doc1', available: true },
            { start_time: '09:00', doctor_id: 'doc2', available: true }, // Diferente doctor
          ]
        })
      });
    });

    afterEach(() => {
      jest.restoreAllMocks();
    });

    it('debe deduplicar time slots correctamente', async () => {
      render(<AIEnhancedRescheduleModal {...defaultProps} />);

      // Seleccionar una fecha para cargar time slots
      fireEvent.click(screen.getByTestId('mock-date-select'));

      // Esperar a que se carguen los time slots
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalled();
      });

      // Verificar que no hay duplicados en la UI
      // (esto se valida indirectamente a través de la funcionalidad del componente)
      expect(screen.getByTestId('selected-date')).toHaveTextContent('2025-06-01');
    });

    it('debe manejar time slots con diferentes doctores', async () => {
      render(<AIEnhancedRescheduleModal {...defaultProps} />);

      // Seleccionar una fecha para cargar time slots
      fireEvent.click(screen.getByTestId('mock-date-select'));

      // Esperar a que se carguen los time slots
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalled();
      });

      // Verificar que la carga fue exitosa
      expect(screen.getByTestId('selected-date')).toHaveTextContent('2025-06-01');
    });

    it('debe ordenar time slots por hora', async () => {
      // Mock fetch con time slots desordenados
      global.fetch = jest.fn().mockResolvedValue({
        ok: true,
        json: async () => ({
          success: true,
          data: [
            { start_time: '15:00', doctor_id: 'doc1', available: true },
            { start_time: '09:00', doctor_id: 'doc1', available: true },
            { start_time: '12:00', doctor_id: 'doc1', available: true },
          ]
        })
      });

      render(<AIEnhancedRescheduleModal {...defaultProps} />);

      // Seleccionar una fecha para cargar time slots
      fireEvent.click(screen.getByTestId('mock-date-select'));

      // Esperar a que se carguen los time slots
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalled();
      });

      // Verificar que la carga fue exitosa
      expect(screen.getByTestId('selected-date')).toHaveTextContent('2025-06-01');
    });
  });

  describe('Correcciones críticas - Estado de carga en cancelación', () => {
    const mockOnCancelAppointment = jest.fn();

    beforeEach(() => {
      mockOnCancelAppointment.mockClear();
    });

    it('debe manejar correctamente el estado de loading durante cancelación', async () => {
      const slowCancelAppointment = jest.fn().mockImplementation(
        () => new Promise(resolve => setTimeout(resolve, 100))
      );

      render(
        <AIEnhancedRescheduleModal
          {...defaultProps}
          onCancelAppointment={slowCancelAppointment}
        />
      );

      // Abrir modal de cancelación
      const cancelButton = screen.getByText('Cancelar Cita');
      fireEvent.click(cancelButton);

      // Seleccionar motivo y confirmar
      const reasonSelect = screen.getByDisplayValue('Selecciona un motivo');
      fireEvent.change(reasonSelect, { target: { value: 'schedule_conflict' } });

      const confirmButton = screen.getByText('Confirmar Cancelación');
      fireEvent.click(confirmButton);

      // Verificar que muestra estado de loading
      await waitFor(() => {
        expect(screen.getByText('Cancelando...')).toBeInTheDocument();
      });

      // Esperar a que termine la operación
      await waitFor(() => {
        expect(slowCancelAppointment).toHaveBeenCalledWith(
          mockAppointment.id,
          'schedule_conflict',
          'Conflicto de horario'
        );
      }, { timeout: 200 });
    });

    it('debe cerrar ambos modales después de cancelación exitosa', async () => {
      const mockOnCancel = jest.fn();

      render(
        <AIEnhancedRescheduleModal
          {...defaultProps}
          onCancel={mockOnCancel}
          onCancelAppointment={mockOnCancelAppointment}
        />
      );

      // Abrir modal de cancelación
      fireEvent.click(screen.getByText('Cancelar Cita'));

      // Seleccionar motivo y confirmar
      const reasonSelect = screen.getByDisplayValue('Selecciona un motivo');
      fireEvent.change(reasonSelect, { target: { value: 'schedule_conflict' } });

      const confirmButton = screen.getByText('Confirmar Cancelación');
      fireEvent.click(confirmButton);

      await waitFor(() => {
        expect(mockOnCancelAppointment).toHaveBeenCalled();
        expect(mockOnCancel).toHaveBeenCalled(); // Modal principal debe cerrarse
      });
    });

    it('debe prevenir múltiples envíos durante estado de loading', async () => {
      const slowCancelAppointment = jest.fn().mockImplementation(
        () => new Promise(resolve => setTimeout(resolve, 100))
      );

      render(
        <AIEnhancedRescheduleModal
          {...defaultProps}
          onCancelAppointment={slowCancelAppointment}
        />
      );

      // Abrir modal de cancelación
      fireEvent.click(screen.getByText('Cancelar Cita'));

      // Seleccionar motivo
      const reasonSelect = screen.getByDisplayValue('Selecciona un motivo');
      fireEvent.change(reasonSelect, { target: { value: 'schedule_conflict' } });

      const confirmButton = screen.getByText('Confirmar Cancelación');

      // Hacer clic múltiples veces rápidamente
      fireEvent.click(confirmButton);
      fireEvent.click(confirmButton);
      fireEvent.click(confirmButton);

      // Verificar que solo se llamó una vez
      await waitFor(() => {
        expect(slowCancelAppointment).toHaveBeenCalledTimes(1);
      }, { timeout: 200 });
    });
  });
});
