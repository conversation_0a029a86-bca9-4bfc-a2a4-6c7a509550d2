/**
 * Database Integration Tests - Row Level Security (RLS) Policies
 * Tests multi-tenant data isolation and security policies
 */

import { createMockSupabaseClient } from '../utils/test-helpers';
import { MOCK_USERS, MOCK_ORGANIZATIONS, MOCK_APPOINTMENTS } from '../fixtures/optical-simulation-data';

// Mock Supabase server client
jest.mock('@/lib/supabase/server', () => ({
  createClient: jest.fn()
}));

describe('Database RLS Policies', () => {
  let mockSupabase: ReturnType<typeof createMockSupabaseClient>;

  beforeEach(() => {
    mockSupabase = createMockSupabaseClient();
    const { createClient } = require('@/lib/supabase/server');
    createClient.mockReturnValue(mockSupabase);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Organizations Table RLS', () => {
    it('should allow users to read their own organization', async () => {
      const user = MOCK_USERS[0]; // <PERSON> - admin
      const organization = MOCK_ORGANIZATIONS[0];

      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: organization,
              error: null
            })
          })
        })
      });

      const result = await mockSupabase
        .from('organizations')
        .select('*')
        .eq('id', user.organization_id)
        .single();

      expect(result.data).toEqual(organization);
      expect(mockSupabase.from).toHaveBeenCalledWith('organizations');
    });

    it('should prevent users from accessing other organizations', async () => {
      const unauthorizedOrgId = 'org_unauthorized_001';

      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: null,
              error: { message: 'Row Level Security policy violation' }
            })
          })
        })
      });

      const result = await mockSupabase
        .from('organizations')
        .select('*')
        .eq('id', unauthorizedOrgId)
        .single();

      expect(result.error).toBeTruthy();
      expect(result.error.message).toContain('Row Level Security');
    });

    it('should allow admins to update their organization', async () => {
      const admin = MOCK_USERS.find(u => u.role === 'admin');
      const updateData = { name: 'Updated Organization Name' };

      mockSupabase.from.mockReturnValue({
        update: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            select: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue({
                data: { ...MOCK_ORGANIZATIONS[0], ...updateData },
                error: null
              })
            })
          })
        })
      });

      const result = await mockSupabase
        .from('organizations')
        .update(updateData)
        .eq('id', admin?.organization_id)
        .select()
        .single();

      expect(result.data.name).toBe(updateData.name);
      expect(mockSupabase.from).toHaveBeenCalledWith('organizations');
    });

    it('should prevent non-admin users from updating organization', async () => {
      const patient = MOCK_USERS.find(u => u.role === 'patient');
      const updateData = { name: 'Unauthorized Update' };

      mockSupabase.from.mockReturnValue({
        update: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            select: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue({
                data: null,
                error: { message: 'Insufficient privileges' }
              })
            })
          })
        })
      });

      const result = await mockSupabase
        .from('organizations')
        .update(updateData)
        .eq('id', patient?.organization_id)
        .select()
        .single();

      expect(result.error).toBeTruthy();
      expect(result.error.message).toContain('Insufficient privileges');
    });
  });

  describe('Appointments Table RLS', () => {
    it('should allow patients to read their own appointments', async () => {
      const patient = MOCK_USERS.find(u => u.role === 'patient');
      const patientAppointments = MOCK_APPOINTMENTS.filter(
        apt => apt.patient_id === patient?.id
      );

      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            order: jest.fn().mockResolvedValue({
              data: patientAppointments,
              error: null
            })
          })
        })
      });

      const result = await mockSupabase
        .from('appointments')
        .select('*')
        .eq('patient_id', patient?.id)
        .order('appointment_date');

      expect(result.data).toEqual(patientAppointments);
      expect(result.data?.length).toBeGreaterThan(0);
    });

    it('should allow doctors to read appointments in their organization', async () => {
      const doctor = MOCK_USERS.find(u => u.role === 'doctor');
      const doctorAppointments = MOCK_APPOINTMENTS.filter(
        apt => apt.organization_id === doctor?.organization_id
      );

      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            order: jest.fn().mockResolvedValue({
              data: doctorAppointments,
              error: null
            })
          })
        })
      });

      const result = await mockSupabase
        .from('appointments')
        .select('*')
        .eq('organization_id', doctor?.organization_id)
        .order('appointment_date');

      expect(result.data).toEqual(doctorAppointments);
    });

    it('should prevent patients from reading other patients appointments', async () => {
      const patient1 = MOCK_USERS.find(u => u.role === 'patient' && u.id === 'user_maria_001');
      const patient2Id = 'user_juan_001';

      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            order: jest.fn().mockResolvedValue({
              data: [],
              error: null
            })
          })
        })
      });

      const result = await mockSupabase
        .from('appointments')
        .select('*')
        .eq('patient_id', patient2Id)
        .order('appointment_date');

      // Should return empty array due to RLS
      expect(result.data).toEqual([]);
    });

    it('should allow appointment creation with proper validation', async () => {
      const newAppointment = {
        patient_id: 'user_maria_001',
        doctor_id: 'user_ana_001',
        organization_id: 'org_visualcare_001',
        appointment_date: '2024-01-20',
        start_time: '14:00',
        end_time: '14:45',
        status: 'scheduled'
      };

      mockSupabase.from.mockReturnValue({
        insert: jest.fn().mockReturnValue({
          select: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: { id: 'apt_new_001', ...newAppointment },
              error: null
            })
          })
        })
      });

      const result = await mockSupabase
        .from('appointments')
        .insert(newAppointment)
        .select()
        .single();

      expect(result.data).toMatchObject(newAppointment);
      expect(result.data.id).toBeDefined();
    });

    it('should prevent appointment creation across organizations', async () => {
      const crossOrgAppointment = {
        patient_id: 'user_maria_001',
        doctor_id: 'user_unauthorized_doctor',
        organization_id: 'org_unauthorized_001',
        appointment_date: '2024-01-20',
        start_time: '14:00',
        end_time: '14:45',
        status: 'scheduled'
      };

      mockSupabase.from.mockReturnValue({
        insert: jest.fn().mockReturnValue({
          select: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: null,
              error: { message: 'Organization mismatch violation' }
            })
          })
        })
      });

      const result = await mockSupabase
        .from('appointments')
        .insert(crossOrgAppointment)
        .select()
        .single();

      expect(result.error).toBeTruthy();
      expect(result.error.message).toContain('Organization mismatch');
    });
  });

  describe('Profiles Table RLS', () => {
    it('should allow users to read their own profile', async () => {
      const user = MOCK_USERS[0];

      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: user.profile,
              error: null
            })
          })
        })
      });

      const result = await mockSupabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      expect(result.data).toEqual(user.profile);
    });

    it('should allow users to update their own profile', async () => {
      const user = MOCK_USERS[0];
      const updateData = { full_name: 'Updated Name' };

      mockSupabase.from.mockReturnValue({
        update: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            select: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue({
                data: { ...user.profile, ...updateData },
                error: null
              })
            })
          })
        })
      });

      const result = await mockSupabase
        .from('profiles')
        .update(updateData)
        .eq('id', user.id)
        .select()
        .single();

      expect(result.data.full_name).toBe(updateData.full_name);
    });

    it('should prevent users from updating other users profiles', async () => {
      const user1 = MOCK_USERS[0];
      const user2 = MOCK_USERS[1];
      const updateData = { full_name: 'Unauthorized Update' };

      mockSupabase.from.mockReturnValue({
        update: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            select: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue({
                data: null,
                error: { message: 'Profile access denied' }
              })
            })
          })
        })
      });

      const result = await mockSupabase
        .from('profiles')
        .update(updateData)
        .eq('id', user2.id)
        .select()
        .single();

      expect(result.error).toBeTruthy();
      expect(result.error.message).toContain('Profile access denied');
    });
  });

  describe('Multi-tenant Data Isolation', () => {
    it('should ensure complete data isolation between organizations', async () => {
      const org1Users = MOCK_USERS.filter(u => u.organization_id === 'org_visualcare_001');
      const org2Id = 'org_competitor_001';

      // Mock query for organization 2 data (should return empty)
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockResolvedValue({
            data: [],
            error: null
          })
        })
      });

      const result = await mockSupabase
        .from('profiles')
        .select('*')
        .eq('organization_id', org2Id);

      expect(result.data).toEqual([]);
      expect(result.data?.length).toBe(0);
    });

    it('should validate organization context in all operations', async () => {
      const operations = [
        'appointments',
        'profiles',
        'doctor_schedules',
        'services'
      ];

      for (const table of operations) {
        mockSupabase.from.mockReturnValue({
          select: jest.fn().mockReturnValue({
            eq: jest.fn().mockResolvedValue({
              data: [],
              error: null
            })
          })
        });

        const result = await mockSupabase
          .from(table)
          .select('*')
          .eq('organization_id', 'org_visualcare_001');

        expect(mockSupabase.from).toHaveBeenCalledWith(table);
        expect(result.data).toBeDefined();
      }
    });
  });
});
