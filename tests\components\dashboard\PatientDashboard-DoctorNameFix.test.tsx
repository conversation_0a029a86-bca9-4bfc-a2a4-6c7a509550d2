/**
 * @jest-environment jsdom
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import PatientDashboard from '@/components/dashboard/PatientDashboard';

// Mock the required contexts and hooks
jest.mock('@/contexts/auth-context', () => ({
  useAuth: () => ({
    profile: {
      id: 'patient-1',
      first_name: '<PERSON>',
      last_name: '<PERSON>',
      role: 'patient'
    }
  })
}));

jest.mock('@/contexts/tenant-context', () => ({
  useTenant: () => ({
    organization: {
      id: 'org-1',
      name: 'Clínica Test'
    }
  })
}));

jest.mock('@/components/ui/NotificationSystem', () => ({
  useNotificationHelpers: () => ({
    showSuccess: jest.fn(),
    showError: jest.fn(),
    showNetworkError: jest.fn(),
    showAppointmentSuccess: jest.fn()
  })
}));

// Mock the API calls
jest.mock('@/lib/supabase/client', () => ({
  supabase: {
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          order: jest.fn(() => ({
            limit: jest.fn(() => Promise.resolve({
              data: [],
              error: null
            }))
          }))
        }))
      }))
    }))
  }
}));

// Mock the lazy components
jest.mock('@/components/chat/ChatBotLazy', () => {
  return function MockChatBotLazy() {
    return <div data-testid="chatbot">ChatBot</div>;
  };
});

describe('PatientDashboard - Doctor Name Fix', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('transformToAppointmentData function', () => {
    // We need to access the internal function, so we'll test it indirectly
    // by testing the component behavior with different doctor_name values

    it('should handle null doctor_name without crashing', async () => {
      // Mock API response with null doctor_name
      const mockAppointmentWithNullDoctor = {
        id: 'apt-1',
        doctor_name: null,
        service_name: 'Consulta General',
        appointment_date: '2024-12-20',
        start_time: '10:00:00',
        end_time: '10:30:00',
        status: 'confirmed',
        notes: 'Cita de prueba',
        location_name: 'Sede Principal'
      };

      // Mock the supabase client to return our test data
      const mockSupabase = require('@/lib/supabase/client').supabase;
      mockSupabase.from.mockReturnValue({
        select: jest.fn(() => ({
          eq: jest.fn(() => ({
            order: jest.fn(() => ({
              limit: jest.fn(() => Promise.resolve({
                data: [mockAppointmentWithNullDoctor],
                error: null
              }))
            }))
          }))
        }))
      });

      // Render the component
      render(<PatientDashboard />);

      // The component should render without crashing
      expect(screen.getByText('Bienvenido, Juan')).toBeInTheDocument();
    });

    it('should handle undefined doctor_name without crashing', async () => {
      // Mock API response with undefined doctor_name
      const mockAppointmentWithUndefinedDoctor = {
        id: 'apt-2',
        doctor_name: undefined,
        service_name: 'Consulta Especializada',
        appointment_date: '2024-12-21',
        start_time: '14:00:00',
        end_time: '14:30:00',
        status: 'pending',
        notes: null,
        location_name: null
      };

      // Mock the supabase client to return our test data
      const mockSupabase = require('@/lib/supabase/client').supabase;
      mockSupabase.from.mockReturnValue({
        select: jest.fn(() => ({
          eq: jest.fn(() => ({
            order: jest.fn(() => ({
              limit: jest.fn(() => Promise.resolve({
                data: [mockAppointmentWithUndefinedDoctor],
                error: null
              }))
            }))
          }))
        }))
      });

      // Render the component
      render(<PatientDashboard />);

      // The component should render without crashing
      expect(screen.getByText('Bienvenido, Juan')).toBeInTheDocument();
    });

    it('should handle empty string doctor_name without crashing', async () => {
      // Mock API response with empty string doctor_name
      const mockAppointmentWithEmptyDoctor = {
        id: 'apt-3',
        doctor_name: '',
        service_name: 'Consulta de Seguimiento',
        appointment_date: '2024-12-22',
        start_time: '09:00:00',
        end_time: '09:30:00',
        status: 'confirmed',
        notes: 'Seguimiento post-operatorio',
        location_name: 'Sede Norte'
      };

      // Mock the supabase client to return our test data
      const mockSupabase = require('@/lib/supabase/client').supabase;
      mockSupabase.from.mockReturnValue({
        select: jest.fn(() => ({
          eq: jest.fn(() => ({
            order: jest.fn(() => ({
              limit: jest.fn(() => Promise.resolve({
                data: [mockAppointmentWithEmptyDoctor],
                error: null
              }))
            }))
          }))
        }))
      });

      // Render the component
      render(<PatientDashboard />);

      // The component should render without crashing
      expect(screen.getByText('Bienvenido, Juan')).toBeInTheDocument();
    });

    it('should handle valid doctor_name correctly', async () => {
      // Mock API response with valid doctor_name
      const mockAppointmentWithValidDoctor = {
        id: 'apt-4',
        doctor_name: 'Dr. María González Rodríguez',
        service_name: 'Cardiología',
        appointment_date: '2024-12-23',
        start_time: '11:00:00',
        end_time: '11:30:00',
        status: 'confirmed',
        notes: 'Consulta cardiológica',
        location_name: 'Sede Central'
      };

      // Mock the supabase client to return our test data
      const mockSupabase = require('@/lib/supabase/client').supabase;
      mockSupabase.from.mockReturnValue({
        select: jest.fn(() => ({
          eq: jest.fn(() => ({
            order: jest.fn(() => ({
              limit: jest.fn(() => Promise.resolve({
                data: [mockAppointmentWithValidDoctor],
                error: null
              }))
            }))
          }))
        }))
      });

      // Render the component
      render(<PatientDashboard />);

      // The component should render without crashing
      expect(screen.getByText('Bienvenido, Juan')).toBeInTheDocument();
    });
  });

  describe('Error Prevention', () => {
    it('should not throw TypeError when doctor_name is null', () => {
      // This test ensures that the split() error is fixed
      expect(() => {
        render(<PatientDashboard />);
      }).not.toThrow();
    });

    it('should display fallback text for missing doctor names', async () => {
      // Test that the component shows appropriate fallback text
      render(<PatientDashboard />);
      
      // Should not crash and should show the welcome message
      expect(screen.getByText('Bienvenido, Juan')).toBeInTheDocument();
    });
  });
});

/**
 * Test Documentation:
 * 
 * This test suite verifies that the PatientDashboard component correctly handles
 * null, undefined, and empty doctor_name values without throwing TypeError.
 * 
 * The fix implemented:
 * 1. Updated PatientAppointment interface to allow null/undefined doctor_name
 * 2. Updated PatientStats interface to allow null/undefined doctor_name
 * 3. Modified transformToAppointmentData to safely handle null/undefined values
 * 4. Added fallback text "Doctor no asignado" for missing doctor names
 * 5. Improved name parsing logic to handle edge cases
 * 
 * Error prevented: TypeError: Cannot read properties of undefined (reading 'split')
 */
