/**
 * Dashboard Hydration Fix Tests
 *
 * Tests for the intermittent React hydration error fix in dashboard layout
 * Validates authentication state management and webpack module loading
 *
 * <AUTHOR> Development Team
 * @date 2025-01-28
 */

import React from 'react';
import { render, screen, waitFor, act } from '@testing-library/react';
import { jest } from '@jest/globals';
import { DashboardErrorBoundary } from '@/components/error-boundary/DashboardErrorBoundary';

// Mock Next.js router
const mockPush = jest.fn();
const mockRouter = {
  push: mockPush,
  replace: jest.fn(),
  prefetch: jest.fn(),
  back: jest.fn(),
  forward: jest.fn(),
  refresh: jest.fn(),
  pathname: '/dashboard',
  query: {},
  asPath: '/dashboard'
};

jest.mock('next/navigation', () => ({
  useRouter: () => mockRouter,
  usePathname: () => '/dashboard',
  useSearchParams: () => new URLSearchParams()
}));

// Mock hydration-safe utilities
jest.mock('@/utils/hydration-safe', () => ({
  useIsClient: jest.fn(() => true)
}));

// Mock AppointmentDataProvider
jest.mock('@/contexts/AppointmentDataProvider', () => ({
  AppointmentDataProvider: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="appointment-data-provider">{children}</div>
  )
}));

describe('Dashboard Hydration Fix', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockPush.mockClear();

    // Reset console methods
    jest.spyOn(console, 'error').mockImplementation(() => {});
    jest.spyOn(console, 'warn').mockImplementation(() => {});
    jest.spyOn(console, 'log').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Error Boundary Integration', () => {
    it('should catch and handle webpack module loading errors', async () => {
      // Mock a component that throws a webpack error
      const ProblematicComponent = () => {
        throw new Error('Cannot read properties of undefined (reading \'call\')');
      };

      render(
        <DashboardErrorBoundary>
          <ProblematicComponent />
        </DashboardErrorBoundary>
      );

      await waitFor(() => {
        expect(screen.getByText('Error de Carga del Sistema')).toBeInTheDocument();
        expect(screen.getByText(/problema al cargar componentes del dashboard/)).toBeInTheDocument();
      });
    });

    it('should catch and handle authentication errors', async () => {
      const AuthErrorComponent = () => {
        throw new Error('useAuth must be used within an AuthProvider');
      };

      render(
        <DashboardErrorBoundary>
          <AuthErrorComponent />
        </DashboardErrorBoundary>
      );

      await waitFor(() => {
        expect(screen.getByText('Error de Autenticación')).toBeInTheDocument();
        expect(screen.getByText(/problema con la autenticación/)).toBeInTheDocument();
      });
    });

    it('should catch and handle hydration errors', async () => {
      const HydrationErrorComponent = () => {
        throw new Error('Hydration failed because the initial UI does not match');
      };

      render(
        <DashboardErrorBoundary>
          <HydrationErrorComponent />
        </DashboardErrorBoundary>
      );

      await waitFor(() => {
        expect(screen.getByText('Error de Sincronización')).toBeInTheDocument();
        expect(screen.getByText(/aplicación necesita sincronizarse/)).toBeInTheDocument();
      });
    });
  });

  describe('Webpack Module Monitor Fix', () => {
    it('should initialize webpack module monitor without syntax errors', async () => {
      // Mock window and webpack require
      const mockWebpackRequire = {
        e: jest.fn().mockResolvedValue({}),
        cache: {}
      };

      Object.defineProperty(window, '__webpack_require__', {
        value: mockWebpackRequire,
        writable: true
      });

      // Import and initialize the monitor
      const { initializeWebpackModuleMonitoring } = await import('@/utils/webpack-module-monitor');

      expect(() => {
        const monitor = initializeWebpackModuleMonitoring();
        expect(monitor).toBeDefined();
      }).not.toThrow();
    });

    it('should handle dynamic import monitoring without eval errors', async () => {
      // Mock unhandled rejection event
      const mockEvent = {
        reason: new Error('Loading chunk 123 failed')
      };

      const { initializeWebpackModuleMonitoring } = await import('@/utils/webpack-module-monitor');
      const monitor = initializeWebpackModuleMonitoring();

      // Simulate unhandled rejection
      const unhandledRejectionEvent = new Event('unhandledrejection') as any;
      unhandledRejectionEvent.reason = mockEvent.reason;

      expect(() => {
        window.dispatchEvent(unhandledRejectionEvent);
      }).not.toThrow();

      // Check that the monitor recorded the error
      const healthMetrics = monitor.getHealthMetrics();
      expect(healthMetrics).toBeDefined();
    });

    it('should not use window.eval for import statement', async () => {
      // Spy on window.eval to ensure it's not called with 'import'
      const evalSpy = jest.spyOn(window, 'eval');

      const { initializeWebpackModuleMonitoring } = await import('@/utils/webpack-module-monitor');
      initializeWebpackModuleMonitoring();

      // Ensure eval was not called with 'import'
      const evalCalls = evalSpy.mock.calls;
      const importEvalCalls = evalCalls.filter(call =>
        call[0] && call[0].toString().includes('import')
      );

      expect(importEvalCalls).toHaveLength(0);

      evalSpy.mockRestore();
    });
  });

});
