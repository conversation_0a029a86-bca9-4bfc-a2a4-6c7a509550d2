/**
 * Appointment Cards Index
 * 
 * Centralized exports for all appointment card components
 * Provides role-specific cards and base components
 * 
 * @version 1.0.0 - Initial role-specific cards
 * <AUTHOR> MVP Team
 * @date 2025-01-28
 */

// Base component
export { AppointmentCardBase, default as AppointmentCard } from '../AppointmentCard';
export type { AppointmentCardBaseProps, AppointmentCardProps, AppointmentData } from '../AppointmentCard';

// Patient-specific components
import PatientAppointmentCardDefault, { PatientDashboardCard, PatientCompactCard } from './PatientAppointmentCard';
export { default as PatientAppointmentCard, PatientDashboardCard, PatientCompactCard } from './PatientAppointmentCard';
export type { PatientAppointmentCardProps } from './PatientAppointmentCard';

// Doctor-specific components
import DoctorAppointmentCardDefault, { DoctorTodayCard, DoctorCompactCard } from './DoctorAppointmentCard';
export { default as DoctorAppointment<PERSON><PERSON>, Doctor<PERSON>odayCard, DoctorCompactCard } from './DoctorAppointmentCard';
export type { DoctorAppointmentCardProps } from './DoctorAppointmentCard';

// Admin-specific components
import AdminAppointmentCardDefault, { AdminDashboardCard, AdminBulkCard } from './AdminAppointmentCard';
export { default as AdminAppointmentCard, AdminDashboardCard, AdminBulkCard } from './AdminAppointmentCard';
export type { AdminAppointmentCardProps } from './AdminAppointmentCard';

// Import base component for fallback
import { default as AppointmentCard } from '../AppointmentCard';

/**
 * Factory function to get the appropriate card component based on user role
 * 
 * @param userRole - The user's role
 * @returns The appropriate card component
 */
export function getAppointmentCardForRole(userRole: string) {
  switch (userRole) {
    case 'patient':
      return PatientAppointmentCardDefault;
    case 'doctor':
      return DoctorAppointmentCardDefault;
    case 'admin':
    case 'staff':
    case 'superadmin':
      return AdminAppointmentCardDefault;
    default:
      return AppointmentCard;
  }
}

/**
 * Factory function to get the dashboard-optimized card component based on user role
 * 
 * @param userRole - The user's role
 * @returns The appropriate dashboard card component
 */
export function getDashboardCardForRole(userRole: string) {
  switch (userRole) {
    case 'patient':
      return PatientDashboardCard;
    case 'doctor':
      return DoctorTodayCard;
    case 'admin':
    case 'staff':
    case 'superadmin':
      return AdminDashboardCard;
    default:
      return AppointmentCard;
  }
}

/**
 * Factory function to get the compact card component based on user role
 *
 * @param userRole - The user's role
 * @returns The appropriate compact card component
 */
export function getCompactCardForRole(userRole: string) {
  switch (userRole) {
    case 'patient':
      return PatientCompactCard;
    case 'doctor':
      return DoctorCompactCard;
    case 'admin':
    case 'staff':
    case 'superadmin':
      return AdminBulkCard;
    default:
      return AppointmentCard;
  }
}

/**
 * Enhanced factory functions from factory.ts
 */
export {
  getCardForContext,
  getDefaultPropsForRole,
  createAppointmentCard,
  isAdministrativeRole,
  isClinicalRole,
  isPatientRole,
  getRoleCapabilities
} from './factory';
