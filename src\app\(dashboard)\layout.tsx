'use client'

import { useAuth } from '@/contexts/auth-context'
import { useTenant } from '@/contexts/tenant-context'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'
import { AppointmentDataProvider } from '@/contexts/AppointmentDataProvider'

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const { user, loading } = useAuth()
  const { organization } = useTenant()
  const router = useRouter()

  useEffect(() => {
    if (!loading && !user) {
      router.push('/login')
    }
  }, [user, loading, router])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  return (
    <AppointmentDataProvider>
      <div className="min-h-screen bg-gray-50">
        {children}
      </div>
    </AppointmentDataProvider>
  )
}
