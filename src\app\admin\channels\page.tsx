/**
 * Unified Channels Admin Page
 * 
 * Main admin page for managing all communication channels using the unified
 * ChannelDashboard component integrated with DashboardLayout.
 * 
 * <AUTHOR> Development Team
 * @date 2025-01-28
 */

'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import DashboardLayout from '@/components/dashboard/DashboardLayout';
import { ChannelDashboard } from '@/components/channels/ChannelDashboard';
import { createClient } from '@/lib/supabase/client';

// =====================================================
// TYPES
// =====================================================

interface UserProfile {
  id: string;
  organization_id: string;
  role: string;
  first_name?: string;
  last_name?: string;
}

// =====================================================
// MAIN COMPONENT
// =====================================================

/**
 * ChannelsAdminPage Component
 * 
 * @description Main admin page for unified channel management.
 * Integrates ChannelDashboard with existing DashboardLayout.
 */
export default function ChannelsAdminPage() {
  // =====================================================
  // STATE MANAGEMENT
  // =====================================================

  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const supabase = createClient();

  // =====================================================
  // AUTHENTICATION & PROFILE
  // =====================================================

  useEffect(() => {
    const checkAuth = async () => {
      try {
        // Check authentication
        const { data: { user }, error: authError } = await supabase.auth.getUser();
        
        if (authError || !user) {
          router.push('/auth/login');
          return;
        }

        // Get user profile
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('id, organization_id, role, first_name, last_name')
          .eq('id', user.id)
          .single();

        if (profileError || !profileData) {
          setError('Failed to load user profile');
          return;
        }

        // Check admin permissions
        if (!['admin', 'superadmin'].includes(profileData.role)) {
          router.push('/dashboard');
          return;
        }

        setProfile(profileData);
      } catch (err) {
        console.error('Authentication error:', err);
        setError('Authentication failed');
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, [router, supabase]);

  // =====================================================
  // LOADING & ERROR STATES
  // =====================================================

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600">Cargando canales...</span>
        </div>
      </DashboardLayout>
    );
  }

  if (error) {
    return (
      <DashboardLayout>
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <h3 className="text-lg font-medium text-red-800 mb-2">Error</h3>
          <p className="text-red-700">{error}</p>
          <button
            type="button"
            onClick={() => window.location.reload()}
            className="mt-3 inline-flex items-center px-3 py-2 border border-red-300 rounded-md text-sm font-medium text-red-700 bg-white hover:bg-red-50"
          >
            Reintentar
          </button>
        </div>
      </DashboardLayout>
    );
  }

  if (!profile) {
    return (
      <DashboardLayout>
        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
          <h3 className="text-lg font-medium text-yellow-800 mb-2">Acceso Denegado</h3>
          <p className="text-yellow-700">No tienes permisos para acceder a esta página.</p>
        </div>
      </DashboardLayout>
    );
  }

  // =====================================================
  // MAIN RENDER
  // =====================================================

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="border-b border-gray-200 pb-4">
          <h1 className="text-2xl font-bold text-gray-900">Canales de Comunicación</h1>
          <p className="mt-1 text-sm text-gray-500">
            Gestiona todos tus canales de comunicación desde un solo lugar
          </p>
        </div>

        {/* Channel Dashboard */}
        <ChannelDashboard
          organizationId={profile.organization_id}
          userRole={profile.role}
        />
      </div>
    </DashboardLayout>
  );
}
