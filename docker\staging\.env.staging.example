# =====================================================
# AGENTSALUD MVP - STAGING ENVIRONMENT VARIABLES
# =====================================================
# Copy this file to .env.staging and update with actual values
# 
# <AUTHOR> DevOps Team
# @date January 2025

# =====================================================
# ENVIRONMENT CONFIGURATION
# =====================================================
ENVIRONMENT=staging
NODE_ENV=production
DEBUG=false

# =====================================================
# APPLICATION URLS
# =====================================================
NEXT_PUBLIC_APP_URL=https://staging.agentsalud.com
EVOLUTION_API_BASE_URL=https://staging-evolution.agentsalud.com
WEBHOOK_GLOBAL_URL=https://staging.agentsalud.com/api/whatsapp/webhook

# =====================================================
# SUPABASE CONFIGURATION (STAGING)
# =====================================================
NEXT_PUBLIC_SUPABASE_URL=https://staging-agentsalud.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_staging_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_staging_supabase_service_role_key
SUPABASE_JWT_SECRET=your_staging_supabase_jwt_secret

# =====================================================
# EVOLUTION API v2 CONFIGURATION
# =====================================================
EVOLUTION_API_KEY=your_staging_evolution_api_key
EVOLUTION_WEBHOOK_VERIFY_TOKEN=your_staging_webhook_verify_token

# WhatsApp Business Configuration
WHATSAPP_BUSINESS_ACCOUNT_ID=your_whatsapp_business_account_id
WHATSAPP_PHONE_NUMBER_ID=your_whatsapp_phone_number_id
WHATSAPP_ACCESS_TOKEN=your_whatsapp_access_token

# =====================================================
# DATABASE CONFIGURATION
# =====================================================
# PostgreSQL for Evolution API
POSTGRES_PASSWORD=your_secure_postgres_password
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_DB=evolution_staging
POSTGRES_USER=evolution_user

# Redis Configuration
REDIS_PASSWORD=your_secure_redis_password
REDIS_HOST=redis
REDIS_PORT=6379

# =====================================================
# OPENAI CONFIGURATION
# =====================================================
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=1000
OPENAI_TEMPERATURE=0.7

# =====================================================
# AUTHENTICATION & SECURITY
# =====================================================
NEXTAUTH_SECRET=your_nextauth_secret_for_staging
NEXTAUTH_URL=https://staging.agentsalud.com

# JWT Configuration
JWT_SECRET=your_jwt_secret_for_staging
JWT_EXPIRES_IN=24h

# Encryption Keys
ENCRYPTION_KEY=your_32_character_encryption_key
ENCRYPTION_IV=your_16_character_iv

# =====================================================
# MONITORING & LOGGING
# =====================================================
# Sentry Configuration
SENTRY_DSN=your_sentry_dsn_for_staging
SENTRY_ENVIRONMENT=staging
SENTRY_RELEASE=staging-latest

# Datadog Configuration
DATADOG_API_KEY=your_datadog_api_key
DATADOG_APP_KEY=your_datadog_app_key
DATADOG_SITE=datadoghq.com

# Grafana Configuration
GRAFANA_ADMIN_USER=admin
GRAFANA_ADMIN_PASSWORD=your_secure_grafana_password

# =====================================================
# EMAIL CONFIGURATION
# =====================================================
# SMTP Configuration for notifications
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=your_smtp_user
SMTP_PASSWORD=your_smtp_password
SMTP_FROM_EMAIL=<EMAIL>
SMTP_FROM_NAME=AgentSalud Staging

# =====================================================
# RATE LIMITING & PERFORMANCE
# =====================================================
# API Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS=true

# Performance Configuration
MAX_CONCURRENT_REQUESTS=50
REQUEST_TIMEOUT_MS=30000
KEEP_ALIVE_TIMEOUT_MS=5000

# =====================================================
# FEATURE FLAGS
# =====================================================
# Feature toggles for staging environment
FEATURE_WHATSAPP_ENABLED=true
FEATURE_TELEGRAM_ENABLED=false
FEATURE_VOICE_ENABLED=false
FEATURE_AI_BOOKING_ENABLED=true
FEATURE_ANALYTICS_ENABLED=true
FEATURE_MAINTENANCE_MODE=false

# =====================================================
# BACKUP & RECOVERY
# =====================================================
# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=agentsalud-staging-backups
BACKUP_S3_REGION=us-east-1

# AWS Configuration for backups
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1

# =====================================================
# SSL/TLS CONFIGURATION
# =====================================================
# SSL Certificate paths (for nginx)
SSL_CERT_PATH=/etc/nginx/ssl/staging.agentsalud.com.crt
SSL_KEY_PATH=/etc/nginx/ssl/staging.agentsalud.com.key
SSL_CHAIN_PATH=/etc/nginx/ssl/staging.agentsalud.com.chain.crt

# =====================================================
# HEALTH CHECKS & MONITORING
# =====================================================
# Health check configuration
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=10
HEALTH_CHECK_RETRIES=3

# Uptime monitoring
UPTIME_ROBOT_API_KEY=your_uptime_robot_api_key
PINGDOM_API_KEY=your_pingdom_api_key

# =====================================================
# COMPLIANCE & SECURITY
# =====================================================
# HIPAA Compliance
HIPAA_AUDIT_ENABLED=true
HIPAA_ENCRYPTION_ENABLED=true
HIPAA_LOG_RETENTION_DAYS=2555

# Security Headers
SECURITY_HEADERS_ENABLED=true
CORS_ORIGIN=https://staging.agentsalud.com
CSP_ENABLED=true

# =====================================================
# DEVELOPMENT & DEBUGGING
# =====================================================
# Debug Configuration (staging-specific)
DEBUG_EVOLUTION_API=false
DEBUG_WEBHOOKS=true
DEBUG_AI_PROCESSING=false
DEBUG_DATABASE_QUERIES=false

# Logging Levels
LOG_LEVEL=info
LOG_FORMAT=json
LOG_MAX_FILES=10
LOG_MAX_SIZE=10m

# =====================================================
# TESTING CONFIGURATION
# =====================================================
# Test data configuration
TEST_ORGANIZATION_ID=test-org-staging
TEST_USER_EMAIL=<EMAIL>
TEST_WHATSAPP_NUMBER=+************

# Load testing
LOAD_TEST_ENABLED=false
LOAD_TEST_CONCURRENT_USERS=10
LOAD_TEST_DURATION_MINUTES=5

# =====================================================
# THIRD-PARTY INTEGRATIONS
# =====================================================
# Twilio (for future voice integration)
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=your_twilio_phone_number

# Stripe (for future payment integration)
STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret

# =====================================================
# DEPLOYMENT CONFIGURATION
# =====================================================
# Deployment metadata
DEPLOYMENT_VERSION=staging-v1.0.0
DEPLOYMENT_DATE=2025-01-28
DEPLOYMENT_COMMIT_SHA=staging-commit-sha
DEPLOYMENT_BRANCH=staging

# Container configuration
CONTAINER_MEMORY_LIMIT=2g
CONTAINER_CPU_LIMIT=1
CONTAINER_RESTART_POLICY=unless-stopped

# =====================================================
# CUSTOM STAGING SETTINGS
# =====================================================
# Staging-specific overrides
STAGING_DATA_RETENTION_DAYS=90
STAGING_MAX_ORGANIZATIONS=10
STAGING_MAX_USERS_PER_ORG=50
STAGING_RATE_LIMIT_RELAXED=true

# Demo data
DEMO_DATA_ENABLED=true
DEMO_RESET_SCHEDULE=0 0 * * 0

# =====================================================
# NOTES
# =====================================================
# 1. Replace all placeholder values with actual staging credentials
# 2. Ensure all secrets are properly secured and rotated regularly
# 3. Use strong passwords and encryption keys
# 4. Configure monitoring alerts for all critical services
# 5. Test backup and recovery procedures regularly
# 6. Keep this file secure and never commit to version control
