{"timestamp": "2025-06-01T02:33:51.236Z", "totalDuration": 108, "results": {"phase1": {"duration": 24, "tests": [{"test": "ImmutableDateSystem Core", "date": "2025-01-15", "timezone": "UTC", "success": true}, {"test": "ImmutableDateSystem Core", "date": "2025-01-31", "timezone": "UTC", "success": true}, {"test": "ImmutableDateSystem Core", "date": "2025-02-28", "timezone": "UTC", "success": true}, {"test": "ImmutableDateSystem Core", "date": "2025-03-01", "timezone": "UTC", "success": true}, {"test": "ImmutableDateSystem Core", "date": "2025-12-31", "timezone": "UTC", "success": true}, {"test": "ImmutableDateSystem Core", "date": "2026-01-01", "timezone": "UTC", "success": true}, {"test": "ImmutableDateSystem Core", "date": "2024-02-29", "timezone": "UTC", "success": true}, {"test": "ImmutableDateSystem Core", "date": "2025-03-09", "timezone": "UTC", "success": true}, {"test": "ImmutableDateSystem Core", "date": "2025-01-15", "timezone": "America/New_York", "success": true}, {"test": "ImmutableDateSystem Core", "date": "2025-01-31", "timezone": "America/New_York", "success": true}, {"test": "ImmutableDateSystem Core", "date": "2025-02-28", "timezone": "America/New_York", "success": true}, {"test": "ImmutableDateSystem Core", "date": "2025-03-01", "timezone": "America/New_York", "success": true}, {"test": "ImmutableDateSystem Core", "date": "2025-12-31", "timezone": "America/New_York", "success": true}, {"test": "ImmutableDateSystem Core", "date": "2026-01-01", "timezone": "America/New_York", "success": true}, {"test": "ImmutableDateSystem Core", "date": "2024-02-29", "timezone": "America/New_York", "success": true}, {"test": "ImmutableDateSystem Core", "date": "2025-03-09", "timezone": "America/New_York", "success": true}, {"test": "ImmutableDateSystem Core", "date": "2025-01-15", "timezone": "Europe/London", "success": true}, {"test": "ImmutableDateSystem Core", "date": "2025-01-31", "timezone": "Europe/London", "success": true}, {"test": "ImmutableDateSystem Core", "date": "2025-02-28", "timezone": "Europe/London", "success": true}, {"test": "ImmutableDateSystem Core", "date": "2025-03-01", "timezone": "Europe/London", "success": true}, {"test": "ImmutableDateSystem Core", "date": "2025-12-31", "timezone": "Europe/London", "success": true}, {"test": "ImmutableDateSystem Core", "date": "2026-01-01", "timezone": "Europe/London", "success": true}, {"test": "ImmutableDateSystem Core", "date": "2024-02-29", "timezone": "Europe/London", "success": true}, {"test": "ImmutableDateSystem Core", "date": "2025-03-09", "timezone": "Europe/London", "success": true}, {"test": "ImmutableDateSystem Core", "date": "2025-01-15", "timezone": "Asia/Tokyo", "success": true}, {"test": "ImmutableDateSystem Core", "date": "2025-01-31", "timezone": "Asia/Tokyo", "success": true}, {"test": "ImmutableDateSystem Core", "date": "2025-02-28", "timezone": "Asia/Tokyo", "success": true}, {"test": "ImmutableDateSystem Core", "date": "2025-03-01", "timezone": "Asia/Tokyo", "success": true}, {"test": "ImmutableDateSystem Core", "date": "2025-12-31", "timezone": "Asia/Tokyo", "success": true}, {"test": "ImmutableDateSystem Core", "date": "2026-01-01", "timezone": "Asia/Tokyo", "success": true}, {"test": "ImmutableDateSystem Core", "date": "2024-02-29", "timezone": "Asia/Tokyo", "success": true}, {"test": "ImmutableDateSystem Core", "date": "2025-03-09", "timezone": "Asia/Tokyo", "success": true}, {"test": "Date Parsing Consistency", "input": "2025-01-15", "expected": true, "actual": true, "success": true}, {"test": "Date Parsing Consistency", "input": "2025-1-15", "expected": false, "actual": false, "success": true}, {"test": "Date Parsing Consistency", "input": "2025-01-32", "expected": false, "actual": true, "success": false}, {"test": "Date Parsing Consistency", "input": "2025-13-01", "expected": false, "actual": true, "success": false}, {"test": "Component Integration", "component": "src/components/appointments/WeeklyAvailabilitySelector.tsx", "hasImmutableDateSystem": true, "hasValidation": true, "hasDisplacementCheck": true, "success": true}, {"test": "Component Integration", "component": "src/components/appointments/UnifiedAppointmentFlow.tsx", "hasImmutableDateSystem": true, "hasValidation": true, "hasDisplacementCheck": true, "success": true}, {"test": "Component Integration", "component": "src/lib/core/ImmutableDateSystem.ts", "hasImmutableDateSystem": true, "hasValidation": true, "hasDisplacementCheck": true, "success": true}, {"test": "API Endpoint Validation", "endpoint": "src/app/api/appointments/route.ts", "hasDateValidation": true, "hasTimezoneHandling": true, "success": true}, {"test": "API Endpoint Validation", "endpoint": "src/app/api/doctors/availability/route.ts", "hasDateValidation": true, "hasTimezoneHandling": true, "success": true}], "issues": ["Date parsing inconsistency: 2025-01-32", "Date parsing inconsistency: 2025-13-01"]}, "phase2": {"duration": 18, "tests": [{"test": "Cross-timezone Date Selection", "date": "2025-01-15", "timezone": "UTC", "success": true, "displacement": false}, {"test": "Cross-timezone Date Selection", "date": "2025-02-28", "timezone": "UTC", "success": true, "displacement": false}, {"test": "Cross-timezone Date Selection", "date": "2025-12-31", "timezone": "UTC", "success": true, "displacement": false}, {"test": "Cross-timezone Date Selection", "date": "2025-01-15", "timezone": "America/New_York", "success": true, "displacement": false}, {"test": "Cross-timezone Date Selection", "date": "2025-02-28", "timezone": "America/New_York", "success": true, "displacement": false}, {"test": "Cross-timezone Date Selection", "date": "2025-12-31", "timezone": "America/New_York", "success": true, "displacement": false}, {"test": "Cross-timezone Date Selection", "date": "2025-01-15", "timezone": "America/Los_Angeles", "success": true, "displacement": false}, {"test": "Cross-timezone Date Selection", "date": "2025-02-28", "timezone": "America/Los_Angeles", "success": true, "displacement": false}, {"test": "Cross-timezone Date Selection", "date": "2025-12-31", "timezone": "America/Los_Angeles", "success": true, "displacement": false}, {"test": "Cross-timezone Date Selection", "date": "2025-01-15", "timezone": "Europe/London", "success": true, "displacement": false}, {"test": "Cross-timezone Date Selection", "date": "2025-02-28", "timezone": "Europe/London", "success": true, "displacement": false}, {"test": "Cross-timezone Date Selection", "date": "2025-12-31", "timezone": "Europe/London", "success": true, "displacement": false}, {"test": "Cross-timezone Date Selection", "date": "2025-01-15", "timezone": "Asia/Tokyo", "success": true, "displacement": false}, {"test": "Cross-timezone Date Selection", "date": "2025-02-28", "timezone": "Asia/Tokyo", "success": true, "displacement": false}, {"test": "Cross-timezone Date Selection", "date": "2025-12-31", "timezone": "Asia/Tokyo", "success": true, "displacement": false}, {"test": "Role-based Booking", "date": "2025-06-01", "role": "patient", "success": false, "expectedToFail": true}, {"test": "Role-based Booking", "date": "2025-06-01", "role": "admin", "success": true, "expectedToFail": false}, {"test": "Role-based Booking", "date": "2025-06-01", "role": "staff", "success": true, "expectedToFail": false}, {"test": "Role-based Booking", "date": "2025-06-01", "role": "doctor", "success": true, "expectedToFail": false}, {"test": "Role-based Booking", "date": "2025-06-01", "role": "superadmin", "success": true, "expectedToFail": false}, {"test": "Calendar Navigation", "startDate": "2025-01-13", "nextWeek": "2025-01-20", "prevWeek": "2025-01-06", "success": true}, {"test": "Calendar Navigation", "startDate": "2025-02-24", "nextWeek": "2025-03-03", "prevWeek": "2025-02-17", "success": true}, {"test": "Calendar Navigation", "startDate": "2025-12-29", "nextWeek": "2026-01-05", "prevWeek": "2025-12-22", "success": true}, {"test": "Edge Case", "description": "DST Spring Forward", "date": "2025-03-09", "success": true}, {"test": "Edge Case", "description": "DST Fall Back", "date": "2025-11-02", "success": true}, {"test": "Edge Case", "description": "Leap Year", "date": "2024-02-29", "success": true}, {"test": "Edge Case", "description": "Non-Leap Year February End", "date": "2025-02-28", "success": true}, {"test": "Browser Compatibility", "testFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\agensalud-sonnet4\\tests\\browser-timezone-displacement-test.html", "success": true, "note": "Manual testing required"}], "issues": []}, "phase3": {"duration": 0, "tests": [], "issues": []}, "summary": {"totalTests": 69, "successfulTests": 66, "displacementIssues": 0, "successCriteriaMet": true, "performanceAcceptable": true}}, "conclusion": {"successCriteriaMet": true, "recommendation": "Timezone displacement issue has been resolved successfully"}}