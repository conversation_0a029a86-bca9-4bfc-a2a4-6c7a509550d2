# 🧪 AgentSalud Testing Framework

## 📋 Overview

Comprehensive testing organization structure for AgentSalud MVP, designed around the **OPTICAL_SIMULATION.md** patterns and AI-first medical appointment booking system.

## 🏗️ Testing Architecture

### **Testing Categories**

#### 1. **Unit Tests** (`tests/`)
- **AI Components** (`ai/`) - AI context, hooks, and utilities
- **Appointments** (`appointments/`) - Booking logic and server actions  
- **Organizations** (`organizations/`) - Multi-tenant functionality
- **Profile** (`profile/`) - User management

#### 2. **Integration Tests**
- **Database** (`database/`) - RLS policies and data isolation
- **API** (`api/`) - AI endpoints and appointment APIs
- **Authentication** - Multi-tenant auth flows

#### 3. **End-to-End Tests** (`e2e/`)
- **Complete User Journeys** - AI chat to appointment confirmation
- **Multi-Role Scenarios** - Patient, doctor, admin workflows
- **Cross-Organization Isolation** - Multi-tenant security

#### 4. **Performance Tests** (`performance/`)
- **AI Response Times** - Chat and appointment processing benchmarks
- **Concurrent Users** - Load testing and scalability
- **Memory Usage** - Resource optimization validation

## 🎯 Coverage Goals

### **Critical Components (90% Coverage)**
- `src/contexts/` - Authentication, tenant, and AI contexts
- `src/app/api/` - All API endpoints and server actions

### **Standard Components (80% Coverage)**
- `src/components/` - React components
- `src/lib/` - Utility functions and helpers

### **Current Status**
- **Total Tests**: 54+ passing
- **Coverage**: 93% overall
- **Build Status**: ✅ Passing

## 🛠️ Testing Tools & Setup

### **Core Dependencies**
```json
{
  "jest": "^29.7.0",
  "@testing-library/react": "^16.3.0",
  "@testing-library/jest-dom": "^6.6.3",
  "jest-environment-jsdom": "^29.7.0"
}
```

### **Configuration**
- **Jest Config**: `jest.config.js` - Enhanced with performance settings
- **Setup**: `jest.setup.js` - Global mocks and utilities
- **TypeScript**: Full type support for all tests

## 📊 Test Data & Fixtures

### **OPTICAL_SIMULATION.md Integration**
Based on **Óptica VisualCare** simulation data:

```typescript
// Organizations
const MOCK_ORGANIZATIONS = [
  {
    id: 'org_visualcare_001',
    name: 'Óptica VisualCare',
    slug: 'visualcare',
    // ... complete organization data
  }
];

// Users (Admins, Doctors, Staff, Patients)
const MOCK_USERS = [
  {
    id: 'user_carlos_001',
    email: '<EMAIL>',
    role: 'admin',
    // ... complete user profiles
  }
];

// Appointments & Services
const MOCK_APPOINTMENTS = [
  // Realistic appointment data with proper scheduling
];
```

### **Factory Functions**
```typescript
// Generate test data dynamically
createMockUser(overrides?: Partial<MockUser>): MockUser
createMockOrganization(overrides?: Partial<MockOrganization>): MockOrganization
createMockAppointment(overrides?: Partial<MockAppointment>): MockAppointment
```

## 🔧 Testing Utilities

### **Enhanced Render Function**
```typescript
renderWithProviders(
  ui: ReactElement,
  options: {
    initialUser?: MockUser | null;
    initialOrganization?: MockOrganization | null;
    withAI?: boolean;
  }
)
```

### **Mock Factories**
```typescript
// Supabase client with complete API surface
createMockSupabaseClient()

// AI SDK with realistic response simulation
createMockAISDK()

// localStorage with persistence simulation
createMockLocalStorage()
```

### **Performance Helpers**
```typescript
// Measure execution time
measurePerformance<T>(fn: () => T | Promise<T>): Promise<{ result: T; duration: number }>

// Database transaction simulation
createTestTransaction()
```

## 🚀 Running Tests

### **All Tests**
```bash
npm test                    # Run all tests
npm run test:watch         # Watch mode
npm run test:coverage      # Coverage report
```

### **Specific Categories**
```bash
# Unit tests
npm test tests/ai/
npm test tests/appointments/
npm test tests/organizations/

# Integration tests  
npm test tests/database/
npm test tests/api/

# End-to-end tests
npm test tests/e2e/

# Performance tests
npm test tests/performance/
```

### **Coverage Analysis**
```bash
npm run test:coverage
# Opens coverage report in browser
```

## 📋 Test Scenarios

### **AI-First Features**
- ✅ **Natural Language Processing** - Intent recognition and entity extraction
- ✅ **Conversational Flow** - Multi-turn appointment booking
- ✅ **Context Management** - Conversation history and user preferences
- ✅ **Error Handling** - Graceful degradation and fallbacks

### **Multi-Tenant Security**
- ✅ **Data Isolation** - Organization boundary enforcement
- ✅ **RLS Policies** - Row-level security validation
- ✅ **Role-Based Access** - Permission verification
- ✅ **Cross-Tenant Prevention** - Unauthorized access blocking

### **Appointment System**
- ✅ **Booking Flow** - Complete patient journey
- ✅ **Schedule Management** - Doctor availability and conflicts
- ✅ **Status Transitions** - Scheduled → Confirmed → Completed
- ✅ **Cancellation/Rescheduling** - Modification workflows

### **Performance & Scalability**
- ✅ **Response Times** - AI processing under 2 seconds
- ✅ **Concurrent Users** - 10+ simultaneous requests
- ✅ **Memory Management** - No leaks in repeated operations
- ✅ **Error Recovery** - Fast failure on invalid requests

## 🎯 Best Practices

### **Test Organization**
1. **Descriptive Names** - Clear test intentions
2. **Arrange-Act-Assert** - Consistent test structure
3. **Mock Isolation** - Independent test execution
4. **Data Cleanup** - Reset state between tests

### **AI Testing Patterns**
```typescript
// Mock AI responses with realistic data
mockAI.generateObject.mockResolvedValue({
  object: {
    intent: 'book',
    specialty: 'cardiología',
    confidence: 0.9
  }
});

// Test conversation flows
const conversationFlow = [
  { role: 'user', content: 'Necesito una cita' },
  { role: 'assistant', content: 'Te ayudo a agendar' }
];
```

### **Database Testing**
```typescript
// Test RLS policies
mockSupabase.from('appointments')
  .select('*')
  .eq('organization_id', 'wrong_org')
  // Should return empty due to RLS
```

### **Performance Testing**
```typescript
// Measure and validate response times
const { duration } = await measurePerformance(async () => {
  return await apiHandler(request);
});
expect(duration).toBeLessThan(2000);
```

## 🔍 Debugging Tests

### **Common Issues**
1. **Mock Conflicts** - Clear mocks between tests
2. **Async Timing** - Use `waitFor` for async operations
3. **Context Providers** - Ensure proper provider wrapping
4. **Environment Variables** - Check test environment setup

### **Debug Commands**
```bash
# Run specific test with debug info
npm test -- --verbose tests/ai/ai-context.test.tsx

# Debug failing test
npm test -- --no-coverage --detectOpenHandles tests/failing-test.test.ts
```

## 📈 Continuous Integration

### **GitHub Actions Integration**
```yaml
- name: Run Tests
  run: |
    npm test -- --coverage --watchAll=false
    npm run test:performance
```

### **Quality Gates**
- ✅ **80% Coverage Minimum** - Enforced in CI
- ✅ **All Tests Passing** - No broken tests in main
- ✅ **Performance Benchmarks** - Response time validation
- ✅ **Security Tests** - RLS and auth validation

## 🎉 Success Metrics

### **Current Achievement**
- **54+ Tests Passing** ✅
- **93% Coverage** ✅  
- **AI-First Features Tested** ✅
- **Multi-Tenant Security Validated** ✅
- **Performance Benchmarks Met** ✅

### **MVP Completion**
The testing framework successfully validates:
- ✅ Complete appointment booking flow
- ✅ AI-powered natural language processing
- ✅ Multi-tenant data isolation
- ✅ Role-based access control
- ✅ Performance and scalability requirements

---

**Last Updated**: January 2025  
**Framework Version**: 2.0  
**Status**: ✅ Production Ready
