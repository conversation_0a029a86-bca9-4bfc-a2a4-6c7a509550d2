@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for AgentSalud MVP */
:root {
  /* Medical Color Palette - Professional Healthcare Colors */
  --medical-blue: #0066CC;
  --medical-teal: #00A693;
  --medical-navy: #1E3A8A;
  --warm-orange: #F59E0B;
  --soft-green: #10B981;
  --light-blue: #EBF8FF;
  --neutral-gray: #6B7280;
  --light-gray: #F9FAFB;
  --medical-purple: #7C3AED;
  --medical-red: #DC2626;
}

body {
  font-family: system-ui, -apple-system, sans-serif;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Notification animations */
@keyframes slide-in-up {
  from {
    transform: translateY(100%) translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0) translateX(0);
    opacity: 1;
  }
}

@keyframes slide-out-right {
  from {
    transform: translateY(0) translateX(0);
    opacity: 1;
  }
  to {
    transform: translateY(0) translateX(100%);
    opacity: 0;
  }
}

.animate-slide-in-up {
  animation: slide-in-up 0.3s ease-out;
}

.animate-slide-out-right {
  animation: slide-out-right 0.3s ease-in;
}

/* Focus ring improvements for better accessibility */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
}

/* Touch target improvements for mobile */
.touch-target {
  min-height: 44px;
  min-width: 44px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .bg-blue-50 {
    background-color: #e0f2fe;
  }

  .bg-green-50 {
    background-color: #e8f5e8;
  }

  .bg-red-50 {
    background-color: #ffeaea;
  }

  .bg-yellow-50 {
    background-color: #fffbeb;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .animate-slide-in-up,
  .animate-slide-out-right,
  .animate-pulse,
  .animate-spin {
    animation: none;
  }

  .transition-all,
  .transition-colors,
  .transition-opacity,
  .transition-transform {
    transition: none;
  }
}

/* Medical AI Theme Classes */
.medical-gradient-blue {
  background: linear-gradient(135deg, var(--medical-blue), var(--medical-teal));
}

.medical-gradient-purple {
  background: linear-gradient(135deg, var(--medical-purple), var(--medical-blue));
}

.medical-gradient-green {
  background: linear-gradient(135deg, var(--soft-green), var(--medical-teal));
}

.medical-text-gradient {
  background: linear-gradient(135deg, var(--medical-blue), var(--medical-teal));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* AI Demo Animation */
@keyframes ai-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02);
  }
}

.ai-pulse {
  animation: ai-pulse 2s ease-in-out infinite;
}

/* Professional Medical Shadows */
.medical-shadow {
  box-shadow: 0 4px 20px rgba(0, 102, 204, 0.1);
}

.medical-shadow-lg {
  box-shadow: 0 10px 40px rgba(0, 102, 204, 0.15);
}
