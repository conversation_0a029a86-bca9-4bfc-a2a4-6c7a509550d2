/**
 * Patient Appointments Integration Tests
 * End-to-end validation of the data consistency fix
 * Tests the complete flow from database to UI components
 */

import { describe, it, expect } from '@jest/globals';

// Simulate the complete data flow for <PERSON>'s case
describe('Patient Appointments Integration - <PERSON>', () => {
  
  const mariaProfile = {
    id: '5b361f1e-04b6-4a40-bb61-bd519c0e9be8',
    first_name: '<PERSON>',
    last_name: '<PERSON>',
    email: '<EMAIL>',
    role: 'patient',
    organization_id: '927cecbe-d9e5-43a4-b9d0-25f942ededc4'
  };

  const mariaPatient = {
    id: '61b5606b-3e21-4cbb-93f0-9174538e21a5',
    profile_id: '5b361f1e-04b6-4a40-bb61-bd519c0e9be8',
    organization_id: '927cecbe-d9e5-43a4-b9d0-25f942ededc4'
  };

  const mariaAppointments = [
    {
      id: 'd21c7a05-6cbc-46f5-be2d-4ec19836da52',
      appointment_date: '2025-06-05',
      start_time: '17:30:00',
      status: 'scheduled',
      patient_id: '5b361f1e-04b6-4a40-bb61-bd519c0e9be8', // profile_id
      organization_id: '927cecbe-d9e5-43a4-b9d0-25f942ededc4'
    },
    {
      id: 'd70bbe24-3876-4032-93b6-a206549a5180',
      appointment_date: '2025-05-25', // past appointment
      start_time: '15:30:00',
      status: 'completed',
      patient_id: '5b361f1e-04b6-4a40-bb61-bd519c0e9be8', // profile_id
      organization_id: '927cecbe-d9e5-43a4-b9d0-25f942ededc4'
    }
  ];

  describe('Database Layer Consistency', () => {
    it('should validate foreign key relationships', () => {
      // appointments.patient_id -> profiles.id (CORRECT)
      mariaAppointments.forEach(appointment => {
        expect(appointment.patient_id).toBe(mariaProfile.id);
      });

      // patients.profile_id -> profiles.id (CORRECT)
      expect(mariaPatient.profile_id).toBe(mariaProfile.id);
    });

    it('should confirm appointments are NOT linked to patients.id', () => {
      // This was the source of the bug
      mariaAppointments.forEach(appointment => {
        expect(appointment.patient_id).not.toBe(mariaPatient.id);
      });
    });
  });

  describe('API Layer Consistency', () => {
    
    // Simulate Dashboard Stats API logic
    const simulateDashboardStatsAPI = (patientId: string) => {
      const totalAppointments = mariaAppointments.filter(apt => 
        apt.patient_id === patientId
      );
      
      const now = new Date();
      const currentDate = now.toISOString().split('T')[0];
      
      const upcomingAppointments = mariaAppointments.filter(apt => 
        apt.patient_id === patientId &&
        ['confirmed', 'pending', 'scheduled'].includes(apt.status) &&
        apt.appointment_date >= currentDate
      );

      return {
        totalAppointments: totalAppointments.length,
        upcomingAppointments: upcomingAppointments.length
      };
    };

    // Simulate Appointments API logic
    const simulateAppointmentsAPI = (userId: string, filter?: string) => {
      let filteredAppointments = mariaAppointments.filter(apt => 
        apt.patient_id === userId
      );

      if (filter === 'upcoming') {
        const today = new Date().toISOString().split('T')[0];
        filteredAppointments = filteredAppointments.filter(apt => 
          apt.appointment_date >= today
        );
      } else if (filter === 'past') {
        const today = new Date().toISOString().split('T')[0];
        filteredAppointments = filteredAppointments.filter(apt => 
          apt.appointment_date < today
        );
      }

      return filteredAppointments;
    };

    it('should return consistent counts between Dashboard Stats and Appointments APIs', () => {
      const dashboardStats = simulateDashboardStatsAPI(mariaProfile.id);
      const allAppointments = simulateAppointmentsAPI(mariaProfile.id);
      
      expect(dashboardStats.totalAppointments).toBe(allAppointments.length);
      expect(dashboardStats.totalAppointments).toBe(2); // Based on mock data
    });

    it('should handle upcoming appointments filter correctly', () => {
      const upcomingAppointments = simulateAppointmentsAPI(mariaProfile.id, 'upcoming');
      expect(upcomingAppointments.length).toBe(1); // Only future appointment
      expect(upcomingAppointments[0].appointment_date).toBe('2025-06-05');
    });

    it('should handle past appointments filter correctly (view=history)', () => {
      const pastAppointments = simulateAppointmentsAPI(mariaProfile.id, 'past');
      expect(pastAppointments.length).toBe(1); // Only past appointment
      expect(pastAppointments[0].appointment_date).toBe('2025-05-25');
    });
  });

  describe('Frontend Component Layer', () => {
    
    // Simulate the CORRECTED appointments page logic
    const simulateAppointmentsPageCorrected = (profile: any, filter?: string) => {
      // CORRECTED: Use profile.id directly
      let appointments = mariaAppointments.filter(apt => 
        apt.patient_id === profile.id &&
        apt.organization_id === profile.organization_id
      );

      // Apply date filter
      const today = new Date().toISOString().split('T')[0];
      if (filter === 'upcoming') {
        appointments = appointments.filter(apt => apt.appointment_date >= today);
      } else if (filter === 'past') {
        appointments = appointments.filter(apt => apt.appointment_date < today);
      }

      return appointments;
    };

    // Simulate the OLD (buggy) appointments page logic
    const simulateAppointmentsPageOld = (patientRecord: any, filter?: string) => {
      // OLD BUG: Use patients.id instead of profile.id
      let appointments = mariaAppointments.filter(apt => 
        apt.patient_id === patientRecord.id // This was wrong!
      );

      const today = new Date().toISOString().split('T')[0];
      if (filter === 'upcoming') {
        appointments = appointments.filter(apt => apt.appointment_date >= today);
      } else if (filter === 'past') {
        appointments = appointments.filter(apt => apt.appointment_date < today);
      }

      return appointments;
    };

    it('should show all appointments with corrected logic', () => {
      const appointments = simulateAppointmentsPageCorrected(mariaProfile);
      expect(appointments.length).toBe(2);
    });

    it('should show 0 appointments with old buggy logic', () => {
      const appointments = simulateAppointmentsPageOld(mariaPatient);
      expect(appointments.length).toBe(0); // The bug!
    });

    it('should handle view=history parameter correctly after fix', () => {
      const pastAppointments = simulateAppointmentsPageCorrected(mariaProfile, 'past');
      expect(pastAppointments.length).toBe(1);
      expect(pastAppointments[0].status).toBe('completed');
    });

    it('should show 0 past appointments with old buggy logic', () => {
      const pastAppointments = simulateAppointmentsPageOld(mariaPatient, 'past');
      expect(pastAppointments.length).toBe(0); // The bug that caused "No hay citas pasadas"
    });
  });

  describe('End-to-End Consistency Validation', () => {

    // Define helper functions within this scope
    const simulateAppointmentsPageCorrected = (profile: any, filter?: string) => {
      let appointments = mariaAppointments.filter(apt =>
        apt.patient_id === profile.id &&
        apt.organization_id === profile.organization_id
      );

      const today = new Date().toISOString().split('T')[0];
      if (filter === 'upcoming') {
        appointments = appointments.filter(apt => apt.appointment_date >= today);
      } else if (filter === 'past') {
        appointments = appointments.filter(apt => apt.appointment_date < today);
      }

      return appointments;
    };

    const simulateAppointmentsPageOld = (patientRecord: any, filter?: string) => {
      let appointments = mariaAppointments.filter(apt =>
        apt.patient_id === patientRecord.id
      );

      const today = new Date().toISOString().split('T')[0];
      if (filter === 'upcoming') {
        appointments = appointments.filter(apt => apt.appointment_date >= today);
      } else if (filter === 'past') {
        appointments = appointments.filter(apt => apt.appointment_date < today);
      }

      return appointments;
    };

    it('should maintain data consistency across all layers after fix', () => {
      // Dashboard Stats
      const dashboardStats = mariaAppointments.filter(apt =>
        apt.patient_id === mariaProfile.id
      ).length;

      // Appointments API
      const apiAppointments = mariaAppointments.filter(apt =>
        apt.patient_id === mariaProfile.id
      ).length;

      // Appointments Page (CORRECTED)
      const pageAppointments = simulateAppointmentsPageCorrected(mariaProfile).length;

      // All should be consistent
      expect(dashboardStats).toBe(apiAppointments);
      expect(apiAppointments).toBe(pageAppointments);
      expect(dashboardStats).toBe(2); // Total appointments
    });

    it('should demonstrate the bug was in the appointments page only', () => {
      // Dashboard and API were always correct
      const dashboardCount = mariaAppointments.filter(apt =>
        apt.patient_id === mariaProfile.id
      ).length;

      // Only the appointments page had the bug
      const oldPageCount = simulateAppointmentsPageOld(mariaPatient).length;
      const correctedPageCount = simulateAppointmentsPageCorrected(mariaProfile).length;

      expect(dashboardCount).toBe(2); // Always correct
      expect(oldPageCount).toBe(0);   // The bug
      expect(correctedPageCount).toBe(2); // Fixed
    });
  });

  describe('Multi-tenant Data Isolation', () => {
    it('should maintain organization boundaries', () => {
      const appointmentsWithMixedOrgs = [
        ...mariaAppointments,
        {
          id: 'other-org-appointment',
          appointment_date: '2025-06-01',
          start_time: '10:00:00',
          status: 'scheduled',
          patient_id: mariaProfile.id, // Same patient
          organization_id: 'different-org-id' // Different org
        }
      ];

      const orgFilteredAppointments = appointmentsWithMixedOrgs.filter(apt => 
        apt.patient_id === mariaProfile.id &&
        apt.organization_id === mariaProfile.organization_id
      );

      expect(orgFilteredAppointments.length).toBe(2); // Only María's org appointments
      expect(appointmentsWithMixedOrgs.length).toBe(3); // Total includes other org
    });
  });
});
